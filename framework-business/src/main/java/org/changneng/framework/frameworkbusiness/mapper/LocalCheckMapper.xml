<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.LocalCheckMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.LocalCheck">
    <id column="ID" jdbcType="VARCHAR" property="id"/>
    <result column="TASK_ID" jdbcType="VARCHAR" property="taskId" />
    <result column="OBJECT_NAME" jdbcType="VARCHAR" property="objectName" />
    <result column="LEVEL_CODE" jdbcType="VARCHAR" property="levelCode" />
    <result column="LEVEL_NAME" jdbcType="VARCHAR" property="levelName" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="LEGAL_PERSON" jdbcType="VARCHAR" property="legalPerson" />
    <result column="LEGAL_PHONE" jdbcType="VARCHAR" property="legalPhone" />
    <result column="CHECK_USER_IDS" jdbcType="VARCHAR" property="checkUserIds" />
    <result column="CHECK_USER_NAMES" jdbcType="VARCHAR" property="checkUserNames" />
    <result column="LAW_ENFORC_IDS" jdbcType="VARCHAR" property="lawEnforcIds" />
    <result column="LOCAL_PERSON" jdbcType="VARCHAR" property="localPerson" />
    <result column="LOCAL_PERSON_PHONE" jdbcType="VARCHAR" property="localPersonPhone" />
    <result column="LOCAL_PERSON_JOB" jdbcType="VARCHAR" property="localPersonJob" />
    <result column="CHECK_START_DATE" jdbcType="TIMESTAMP" property="checkStartDate" />
    <result column="CHECK_END_DATE" jdbcType="TIMESTAMP" property="checkEndDate" />
    <result column="IS_ILLEGALACT_CODE" jdbcType="VARCHAR" property="isIllegalactCode" />
    <result column="IS_ILLEGALACT_NAME" jdbcType="VARCHAR" property="isIllegalactName" />
    <result column="CREAT_USER_ID" jdbcType="VARCHAR" property="creatUserId" />
    <result column="CREAT_USER_NAME" jdbcType="VARCHAR" property="creatUserName" />
    <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="UPDATE_USER_ID" jdbcType="VARCHAR" property="updateUserId" />
    <result column="UPDATE_USER_NAME" jdbcType="VARCHAR" property="updateUserName" />
    <result column="DOC_URL" jdbcType="VARCHAR" property="docUrl" />
    <result column="LAW_OBJECT_ID" jdbcType="VARCHAR" property="lawObjectId" />
    <result column="MAKE_UNIT_NAME" jdbcType="VARCHAR"  property="makeUnitName"/>
    <result column= "CONTRIBUTIO_NNAME" jdbcType="VARCHAR" property="contributionName"/>
    <result column= "INFORM_DEPT_NAME" jdbcType="VARCHAR" property="informDeptName"/>
    <result column= "INFORM_LAW_IDS" jdbcType="VARCHAR" property="informLawIds"/>
    <result column= "PARTICIPANT" jdbcType="VARCHAR" property="participant"/>
    <result column= "SAVE_STATUS" jdbcType="VARCHAR" property="saveStatus"/>
    <result column="IS_APP_HANDLE" jdbcType="DECIMAL" property="isAppHandle"/>
    <result column= "RECORD_USER_ID" jdbcType="VARCHAR" property="recordUserId"/>
    <result column= "RECORD_USER_NAME" jdbcType="VARCHAR" property="recordUserName"/>
    <result column= "multiple" jdbcType="VARCHAR" property="multiple"/>
    <result column="ADMIN_NOTICE_NUMBER" jdbcType="VARCHAR" property="administrativeNoticeNumber"/>
    <result column="ADMIN_NOTICE_ATTACH" jdbcType="VARCHAR" property="administrativeNoticeAttachment"/>
    <result column="ATTACH_FILE_NAME" jdbcType="VARCHAR" property="attachmentFileName"/>
    <result column="FORM_TYPE" jdbcType="DECIMAL" property="formType"/>
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="org.changneng.framework.frameworkbusiness.entity.LocalCheck">
    <result column="CHECK_SUMMARY" jdbcType="VARCHAR"  property="checkSummary" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, TASK_ID,CHECK_SUMMARY, OBJECT_NAME, LEVEL_CODE, LEVEL_NAME, ADDRESS, LEGAL_PERSON, LEGAL_PHONE,
    CHECK_USER_IDS, CHECK_USER_NAMES, LAW_ENFORC_IDS, LOCAL_PERSON, LOCAL_PERSON_PHONE,
    LOCAL_PERSON_JOB, CHECK_START_DATE, CHECK_END_DATE, IS_ILLEGALACT_CODE, IS_ILLEGALACT_NAME,
    CREAT_USER_ID, CREAT_USER_NAME, LAST_UPDATE_DATE, UPDATE_USER_ID, UPDATE_USER_NAME,
    DOC_URL,LAW_OBJECT_ID,MAKE_UNIT_NAME,CONTRIBUTIO_NNAME,INFORM_DEPT_NAME,INFORM_LAW_IDS,PARTICIPANT,SAVE_STATUS,IS_APP_HANDLE,
	RECORD_USER_ID,RECORD_USER_NAME,multiple,ADMIN_NOTICE_NUMBER, ADMIN_NOTICE_ATTACH,ATTACH_FILE_NAME,FORM_TYPE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    from LOCAL_CHECK
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from LOCAL_CHECK
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.LocalCheck">
    insert into LOCAL_CHECK (ID, TASK_ID, OBJECT_NAME,
      LEVEL_CODE, LEVEL_NAME, ADDRESS,
      LEGAL_PERSON, LEGAL_PHONE, CHECK_USER_IDS,
      CHECK_USER_NAMES, LAW_ENFORC_IDS, LOCAL_PERSON,
      LOCAL_PERSON_PHONE, LOCAL_PERSON_JOB, CHECK_START_DATE,
      CHECK_END_DATE, IS_ILLEGALACT_CODE, IS_ILLEGALACT_NAME,
      CREAT_USER_ID, CREAT_USER_NAME, LAST_UPDATE_DATE,
      UPDATE_USER_ID, UPDATE_USER_NAME, DOC_URL,
      CHECK_SUMMARY,LAW_OBJECT_ID,MAKE_UNIT_NAME,CONTRIBUTIO_NNAME,INFORM_DEPT_NAME,
      INFORM_LAW_IDS,PARTICIPANT,SAVE_STATUS,IS_APP_HANDLE,multiple)
    values (#{id,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR}, #{objectName,jdbcType=VARCHAR},
      #{levelCode,jdbcType=VARCHAR}, #{levelName,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},
      #{legalPerson,jdbcType=VARCHAR}, #{legalPhone,jdbcType=VARCHAR}, #{checkUserIds,jdbcType=VARCHAR},
      #{checkUserNames,jdbcType=VARCHAR}, #{lawEnforcIds,jdbcType=VARCHAR}, #{localPerson,jdbcType=VARCHAR},
      #{localPersonPhone,jdbcType=VARCHAR}, #{localPersonJob,jdbcType=VARCHAR}, #{checkStartDate,jdbcType=TIMESTAMP},
      #{checkEndDate,jdbcType=TIMESTAMP}, #{isIllegalactCode,jdbcType=VARCHAR}, #{isIllegalactName,jdbcType=VARCHAR},
      #{creatUserId,jdbcType=VARCHAR}, #{creatUserName,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=TIMESTAMP},
      #{updateUserId,jdbcType=VARCHAR}, #{updateUserName,jdbcType=VARCHAR}, #{docUrl,jdbcType=VARCHAR},
      #{checkSummary,jdbcType=BLOB},#{makeUnitName,jdbcType=VARCHAR}) #{lawObjectId,jdbcType=VARCHAR},
      #{contributionName,jdbcType=VARCHAR}, #{informDeptName,jdbcType=VARCHAR},#{informLawIds,jdbcType=VARCHAR},
      #{participant,jdbcType=VARCHAR},#{saveStatus,jdbcType=VARCHAR},#{isAppHandle,jdbcType=DECIMAL},#{multiple,jdbcType=VARCHAR}
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.LocalCheck">
     <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into LOCAL_CHECK
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="taskId != null">
        TASK_ID,
      </if>
      <if test="objectName != null">
        OBJECT_NAME,
      </if>
      <if test="levelCode != null">
        LEVEL_CODE,
      </if>
      <if test="levelName != null">
        LEVEL_NAME,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="legalPerson != null">
        LEGAL_PERSON,
      </if>
      <if test="legalPhone != null">
        LEGAL_PHONE,
      </if>
      <if test="checkUserIds != null">
        CHECK_USER_IDS,
      </if>
      <if test="checkUserNames != null">
        CHECK_USER_NAMES,
      </if>
      <if test="lawEnforcIds != null">
        LAW_ENFORC_IDS,
      </if>
      <if test="localPerson != null">
        LOCAL_PERSON,
      </if>
      <if test="localPersonPhone != null">
        LOCAL_PERSON_PHONE,
      </if>
      <if test="localPersonJob != null">
        LOCAL_PERSON_JOB,
      </if>
      <if test="checkStartDate != null">
        CHECK_START_DATE,
      </if>
      <if test="checkEndDate != null">
        CHECK_END_DATE,
      </if>
      <if test="isIllegalactCode != null">
        IS_ILLEGALACT_CODE,
      </if>
      <if test="isIllegalactName != null">
        IS_ILLEGALACT_NAME,
      </if>
      <if test="creatUserId != null">
        CREAT_USER_ID,
      </if>
      <if test="creatUserName != null">
        CREAT_USER_NAME,
      </if>
      <if test="lastUpdateDate != null">
        LAST_UPDATE_DATE,
      </if>
      <if test="updateUserId != null">
        UPDATE_USER_ID,
      </if>
      <if test="updateUserName != null">
        UPDATE_USER_NAME,
      </if>
      <if test="docUrl != null">
        DOC_URL,
      </if>
      <if test="checkSummary != null">
        CHECK_SUMMARY,
      </if>
       <if test="lawObjectId != null">
        LAW_OBJECT_ID,
      </if>
        <if test="makeUnitName != null">
        MAKE_UNIT_NAME,
      </if>
        <if test="contributionName != null">
        CONTRIBUTIO_NNAME,
      </if>
        <if test="informDeptName != null">
        INFORM_DEPT_NAME,
      </if>  <if test="informLawIds != null">
        INFORM_LAW_IDS,
      </if>
       <if test="participant != null">
        PARTICIPANT,
      </if>  <if test="saveStatus != null">
        SAVE_STATUS,
      </if>
      <if test="isAppHandle != null">
        IS_APP_HANDLE,
      </if>
      <if test="recordUserId != null">
        RECORD_USER_ID,
      </if>
      <if test="recordUserName != null">
        RECORD_USER_NAME,
      </if>
      <if test="multiple != null">
        multiple,
      </if>
      <if test="administrativeNoticeNumber != null">
        ADMIN_NOTICE_NUMBER,
      </if>
      <if test="administrativeNoticeAttachment != null">
        ADMIN_NOTICE_ATTACH,
      </if>
      <if test="attachmentFileName != null">
        ATTACH_FILE_NAME,
      </if>
      </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="objectName != null">
        #{objectName,jdbcType=VARCHAR},
      </if>
      <if test="levelCode != null">
        #{levelCode,jdbcType=VARCHAR},
      </if>
      <if test="levelName != null">
        #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="legalPerson != null">
        #{legalPerson,jdbcType=VARCHAR},
      </if>
      <if test="legalPhone != null">
        #{legalPhone,jdbcType=VARCHAR},
      </if>
      <if test="checkUserIds != null">
        #{checkUserIds,jdbcType=VARCHAR},
      </if>
      <if test="checkUserNames != null">
        #{checkUserNames,jdbcType=VARCHAR},
      </if>
      <if test="lawEnforcIds != null">
        #{lawEnforcIds,jdbcType=VARCHAR},
      </if>
      <if test="localPerson != null">
        #{localPerson,jdbcType=VARCHAR},
      </if>
      <if test="localPersonPhone != null">
        #{localPersonPhone,jdbcType=VARCHAR},
      </if>
      <if test="localPersonJob != null">
        #{localPersonJob,jdbcType=VARCHAR},
      </if>
      <if test="checkStartDate != null">
        #{checkStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="checkEndDate != null">
        #{checkEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isIllegalactCode != null">
        #{isIllegalactCode,jdbcType=VARCHAR},
      </if>
      <if test="isIllegalactName != null">
        #{isIllegalactName,jdbcType=VARCHAR},
      </if>
      <if test="creatUserId != null">
        #{creatUserId,jdbcType=VARCHAR},
      </if>
      <if test="creatUserName != null">
        #{creatUserName,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null">
        #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserName != null">
        #{updateUserName,jdbcType=VARCHAR},
      </if>
      <if test="docUrl != null">
        #{docUrl,jdbcType=VARCHAR},
      </if>
      <if test="checkSummary != null">
        #{checkSummary,jdbcType=VARCHAR},
      </if>
       <if test="lawObjectId != null">
        #{lawObjectId,jdbcType=VARCHAR},
      </if>
       <if test="makeUnitName != null">
        #{makeUnitName,jdbcType=VARCHAR},
      </if>
      <if test="contributionName != null">
        #{contributionName,jdbcType=VARCHAR},
      </if>
        <if test="informDeptName != null">
        #{informDeptName,jdbcType=VARCHAR},
      </if>
        <if test="informLawIds != null">
        #{informLawIds,jdbcType=VARCHAR},
      </if>
        <if test="participant != null">
        #{participant,jdbcType=VARCHAR},
      </if>
        <if test="saveStatus != null">
        #{saveStatus,jdbcType=VARCHAR},
      </if>
        <if test="isAppHandle != null">
        #{isAppHandle,jdbcType=VARCHAR},
      </if>
        <if test="recordUserId != null">
        #{recordUserId,jdbcType=VARCHAR},
      </if>
        <if test="recordUserName != null">
        #{recordUserName,jdbcType=VARCHAR},
      </if>
        <if test="multiple != null">
        #{multiple,jdbcType=VARCHAR},
      </if>
      <if test="administrativeNoticeNumber != null">
        #{administrativeNoticeNumber,jdbcType=VARCHAR},
      </if>
      <if test="administrativeNoticeAttachment != null">
        #{administrativeNoticeAttachment,jdbcType=VARCHAR},
      </if>
      <if test="attachmentFileName != null">
        #{attachmentFileName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.LocalCheck">
    update LOCAL_CHECK
    <set>
      <if test="taskId != null">
        TASK_ID = #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="objectName != null">
        OBJECT_NAME = #{objectName,jdbcType=VARCHAR},
      </if>
      <if test="levelCode != null">
        LEVEL_CODE = #{levelCode,jdbcType=VARCHAR},
      </if>
      <if test="levelName != null">
        LEVEL_NAME = #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="legalPerson != null">
        LEGAL_PERSON = #{legalPerson,jdbcType=VARCHAR},
      </if>
      <if test="legalPhone != null">
        LEGAL_PHONE = #{legalPhone,jdbcType=VARCHAR},
      </if>
      <if test="checkUserIds != null">
        CHECK_USER_IDS = #{checkUserIds,jdbcType=VARCHAR},
      </if>
      <if test="checkUserNames != null">
        CHECK_USER_NAMES = #{checkUserNames,jdbcType=VARCHAR},
      </if>
      <if test="lawEnforcIds != null">
        LAW_ENFORC_IDS = #{lawEnforcIds,jdbcType=VARCHAR},
      </if>
      <if test="localPerson != null">
        LOCAL_PERSON = #{localPerson,jdbcType=VARCHAR},
      </if>
      <if test="localPersonPhone != null">
        LOCAL_PERSON_PHONE = #{localPersonPhone,jdbcType=VARCHAR},
      </if>
      <if test="localPersonJob != null">
        LOCAL_PERSON_JOB = #{localPersonJob,jdbcType=VARCHAR},
      </if>
      <if test="checkStartDate != null">
        CHECK_START_DATE = #{checkStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="checkEndDate != null">
        CHECK_END_DATE = #{checkEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isIllegalactCode != null">
        IS_ILLEGALACT_CODE = #{isIllegalactCode,jdbcType=VARCHAR},
      </if>
      <if test="isIllegalactName != null">
        IS_ILLEGALACT_NAME = #{isIllegalactName,jdbcType=VARCHAR},
      </if>
      <if test="creatUserId != null">
        CREAT_USER_ID = #{creatUserId,jdbcType=VARCHAR},
      </if>
      <if test="creatUserName != null">
        CREAT_USER_NAME = #{creatUserName,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null">
        LAST_UPDATE_DATE = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        UPDATE_USER_ID = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserName != null">
        UPDATE_USER_NAME = #{updateUserName,jdbcType=VARCHAR},
      </if>
      <if test="docUrl != null">
        DOC_URL = #{docUrl,jdbcType=VARCHAR},
      </if>
      <if test="checkSummary != null">
        CHECK_SUMMARY = #{checkSummary,jdbcType=VARCHAR},
      </if>
       <if test="lawObjectId != null">
        LAW_OBJECT_ID = #{lawObjectId,jdbcType=VARCHAR},
      </if>
       <if test="makeUnitName != null">
        MAKE_UNIT_NAME = #{makeUnitName,jdbcType=VARCHAR},
      </if>
        <if test="contributionName != null">
        CONTRIBUTIO_NNAME = #{contributionName,jdbcType=VARCHAR},
      </if>
       <if test="informDeptName != null">
        INFORM_DEPT_NAME = #{informDeptName,jdbcType=VARCHAR},
      </if>
       <if test="informLawIds != null">
        INFORM_LAW_IDS = #{informLawIds,jdbcType=VARCHAR},
      </if>
       <if test="participant != null">
        PARTICIPANT = #{participant,jdbcType=VARCHAR},
      </if>
       <if test="saveStatus != null">
        SAVE_STATUS = #{saveStatus,jdbcType=VARCHAR},
      </if>
       <if test="isAppHandle != null">
        IS_APP_HANDLE = #{isAppHandle,jdbcType=DECIMAL},
      </if>
       <if test="recordUserId != null">
        RECORD_USER_ID = #{recordUserId,jdbcType=VARCHAR},
      </if>
       <if test="recordUserName != null">
        RECORD_USER_NAME = #{recordUserName,jdbcType=VARCHAR},
      </if>
       <if test="multiple != null">
        multiple = #{multiple,jdbcType=VARCHAR},
      </if>
      <if test="administrativeNoticeNumber != null">
        ADMIN_NOTICE_NUMBER = #{administrativeNoticeNumber,jdbcType=VARCHAR},
      </if>
      <if test="administrativeNoticeAttachment != null">
        ADMIN_NOTICE_ATTACH = #{administrativeNoticeAttachment,jdbcType=VARCHAR},
      </if>
      <if test="attachmentFileName != null">
        ATTACH_FILE_NAME = #{attachmentFileName,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="org.changneng.framework.frameworkbusiness.entity.LocalCheck">
    update LOCAL_CHECK
    set TASK_ID = #{taskId,jdbcType=VARCHAR},
      OBJECT_NAME = #{objectName,jdbcType=VARCHAR},
      LEVEL_CODE = #{levelCode,jdbcType=VARCHAR},
      LEVEL_NAME = #{levelName,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      LEGAL_PERSON = #{legalPerson,jdbcType=VARCHAR},
      LEGAL_PHONE = #{legalPhone,jdbcType=VARCHAR},
      CHECK_USER_IDS = #{checkUserIds,jdbcType=VARCHAR},
      CHECK_USER_NAMES = #{checkUserNames,jdbcType=VARCHAR},
      LAW_ENFORC_IDS = #{lawEnforcIds,jdbcType=VARCHAR},
      LOCAL_PERSON = #{localPerson,jdbcType=VARCHAR},
      LOCAL_PERSON_PHONE = #{localPersonPhone,jdbcType=VARCHAR},
      LOCAL_PERSON_JOB = #{localPersonJob,jdbcType=VARCHAR},
      CHECK_START_DATE = #{checkStartDate,jdbcType=TIMESTAMP},
      CHECK_END_DATE = #{checkEndDate,jdbcType=TIMESTAMP},
      IS_ILLEGALACT_CODE = #{isIllegalactCode,jdbcType=VARCHAR},
      IS_ILLEGALACT_NAME = #{isIllegalactName,jdbcType=VARCHAR},
      CREAT_USER_ID = #{creatUserId,jdbcType=VARCHAR},
      CREAT_USER_NAME = #{creatUserName,jdbcType=VARCHAR},
      LAST_UPDATE_DATE = #{lastUpdateDate,jdbcType=TIMESTAMP},
      UPDATE_USER_ID = #{updateUserId,jdbcType=VARCHAR},
      UPDATE_USER_NAME = #{updateUserName,jdbcType=VARCHAR},
      DOC_URL = #{docUrl,jdbcType=VARCHAR},
      CHECK_SUMMARY = #{checkSummary,jdbcType=VARCHAR},
      LAW_OBJECT_ID=#{lawObjectId,jdbcType=VARCHAR},
      MAKE_UNIT_NAME=#{makeUnitName,jdbcType=VARCHAR},
      CONTRIBUTIO_NNAME=#{contributionName,jdbcType=VARCHAR},
      INFORM_DEPT_NAME=#{informDeptName,jdbcType=VARCHAR},
      INFORM_LAW_IDS=#{informLawIds,jdbcType=VARCHAR},
      PARTICIPANT=#{participant,jdbcType=VARCHAR},
      SAVE_STATUS=#{saveStatus,jdbcType=VARCHAR},
      IS_APP_HANDLE=#{isAppHandle,jdbcType=DECIMAL},
      multiple=#{multiple,jdbcType=VARCHAR}
      }
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.LocalCheck">
    update LOCAL_CHECK
    set TASK_ID = #{taskId,jdbcType=VARCHAR},
      OBJECT_NAME = #{objectName,jdbcType=VARCHAR},
      LEVEL_CODE = #{levelCode,jdbcType=VARCHAR},
      LEVEL_NAME = #{levelName,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      LEGAL_PERSON = #{legalPerson,jdbcType=VARCHAR},
      LEGAL_PHONE = #{legalPhone,jdbcType=VARCHAR},
      CHECK_USER_IDS = #{checkUserIds,jdbcType=VARCHAR},
      CHECK_USER_NAMES = #{checkUserNames,jdbcType=VARCHAR},
      LAW_ENFORC_IDS = #{lawEnforcIds,jdbcType=VARCHAR},
      LOCAL_PERSON = #{localPerson,jdbcType=VARCHAR},
      LOCAL_PERSON_PHONE = #{localPersonPhone,jdbcType=VARCHAR},
      LOCAL_PERSON_JOB = #{localPersonJob,jdbcType=VARCHAR},
      CHECK_START_DATE = #{checkStartDate,jdbcType=TIMESTAMP},
      CHECK_END_DATE = #{checkEndDate,jdbcType=TIMESTAMP},
      IS_ILLEGALACT_CODE = #{isIllegalactCode,jdbcType=VARCHAR},
      IS_ILLEGALACT_NAME = #{isIllegalactName,jdbcType=VARCHAR},
      CREAT_USER_ID = #{creatUserId,jdbcType=VARCHAR},
      CREAT_USER_NAME = #{creatUserName,jdbcType=VARCHAR},
      LAST_UPDATE_DATE = #{lastUpdateDate,jdbcType=TIMESTAMP},
      UPDATE_USER_ID = #{updateUserId,jdbcType=VARCHAR},
      UPDATE_USER_NAME = #{updateUserName,jdbcType=VARCHAR},
      DOC_URL = #{docUrl,jdbcType=VARCHAR},
      LAW_OBJECT_ID=#{lawObjectId,jdbcType=VARCHAR},
      MAKE_UNIT_NAME=#{makeUnitName,jdbcType=VARCHAR},
      CONTRIBUTIO_NNAME=#{contributionName,jdbcType=VARCHAR},
      INFORM_DEPT_NAME=#{informDeptName,jdbcType=VARCHAR},
      INFORM_LAW_IDS=#{informLawIds,jdbcType=VARCHAR},
      PARTICIPANT=#{participant,jdbcType=VARCHAR},
      SAVE_STATUS=#{saveStatus,jdbcType=VARCHAR},
      IS_APP_HANDLE=#{isAppHandle,jdbcType=DECIMAL},
      multiple=#{multiple,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <!--  getLocalCheickItem 根据任务的id 查询现在检查表信息-->
    <select id="getLocalCheickItem" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    from LOCAL_CHECK
    where TASK_ID = #{taskId,jdbcType=VARCHAR}
  </select>
  <!--updateDocUrlByLocalCheck 根据主键的id修改url  -->

    <update id="updateDocUrlByLocalCheck" parameterType="org.changneng.framework.frameworkbusiness.entity.LocalCheck">
    update LOCAL_CHECK
    set
      DOC_URL = #{docUrl,jdbcType=VARCHAR}
    where ID = #{localChickId,jdbcType=VARCHAR}
  </update>
   <select id="getLocalCheickForColumn" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    	ID , SAVE_STATUS, OBJECT_NAME, LOCAL_PERSON, LAW_ENFORC_IDS, CHECK_USER_NAMES, CHECK_USER_IDS, LEGAL_PHONE, ADDRESS, RECORD_USER_NAME
    from LOCAL_CHECK
    where TASK_ID = #{taskId,jdbcType=VARCHAR}
  </select>
  <!-- selectLocalItemByTaskId根据任务id查询现场检查 -->
   <select id="selectLocalItemByTaskId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    	ID
    from LOCAL_CHECK
    where TASK_ID = #{taskId,jdbcType=VARCHAR}
  </select>
  <update id="updateObjNameByTaskId" parameterType="java.lang.String">
     update LOCAL_CHECK
      set OBJECT_NAME = #{objectName,jdbcType=VARCHAR} where task_id = #{taskId,jdbcType=VARCHAR}
  </update>

  <!-- -↓↓↓↓↓↓↓↓↓↓- -->
  <!-- 根据任务ID和表单类型查询现场检查记录 -->
  <select id="selectByTaskIdAndFormType" resultMap="ResultMapWithBLOBs">
    SELECT
    <include refid="Base_Column_List" />
    FROM LOCAL_CHECK
    WHERE TASK_ID = #{taskId,jdbcType=VARCHAR} AND FORM_TYPE = #{formType,jdbcType=DECIMAL}
  </select>
  <!--  -↑↑↑↑↑↑ -->

</mapper>
